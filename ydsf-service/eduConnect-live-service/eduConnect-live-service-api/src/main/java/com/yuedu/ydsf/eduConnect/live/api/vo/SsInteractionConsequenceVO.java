package com.yuedu.ydsf.eduConnect.live.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 互动结果表
 *
 * <AUTHOR>
 * @date 2024/11/05
 */
@Data
@Schema(description = "互动结果表展示对象")
public class SsInteractionConsequenceVO {

  /** 主键ID */
  @Schema(description = "主键ID")
  private Long id;

  /** 互动设置ID */
  @Schema(description = "互动设置ID")
  private Long interactionSettingId;

  /** 课次ID */
  @Schema(description = "课次ID")
  private Long classTimeId;

  /** 设备ID */
  @Schema(description = "设备ID")
  private Long deviceId;

  /** 校区ID */
  @Schema(description = "校区ID")
  private Long campusId;

  /** 教室ID */
  @Schema(description = "教室ID")
  private Long classRoomId;

  /** 校管家学生ID */
  @Schema(description = "校管家学生ID")
  private String studentId;

  /** 校管家学号 */
  @Schema(description = "校管家学号")
  private String studentNo;

  /** 校管家学生名称 */
  @Schema(description = "校管家学生名称")
  private String studentName;

  /** 抢红包-抢到积分数量 */
  @Schema(description = "抢红包-抢到积分数量")
  private Integer integralNumber;

  /** 答题抢票器-选项 */
  @Schema(description = "答题抢票器-选项")
  private String answerOption;

  /** 创建时间 */
  @Schema(description = "创建时间")
  private LocalDateTime ctime;

  /** 创建者 */
  @Schema(description = "创建者")
  private String creator;

  /** 编辑时间 */
  @Schema(description = "编辑时间")
  private LocalDateTime mtime;

  /** 编辑者 */
  @Schema(description = "编辑者")
  private String modifer;

  /** 删除标记 */
  @Schema(description = "删除标记")
  private Byte delFlag;
}
