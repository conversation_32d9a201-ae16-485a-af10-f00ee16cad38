package com.yuedu.ydsf.eduConnect.live.api.vo;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.format.annotation.DateTimeFormat;

/**
* 课次信息表
*
* <AUTHOR>
* @date  2024/11/01
*/
@Data
@Schema(description = "课次信息表展示对象")
public class SsClassTimeVO {


    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 声网UUID
     */
    @Schema(description = "声网UUID")
    private String roomUuid;

    /**
     * 班级ID
     */
    @Schema(description = "班级ID")
    private Long classId;

    /**
     * 排课ID
     */
    @Schema(description = "排课ID")
    private Long courseScheduleId;

    /**
     * 排课书籍ID
     */
    @Schema(description = "排课书籍ID")
    private Long courseScheduleBooksId;

    /**
     * 排课规则ID
     */
    @Schema(description = "排课规则ID")
    private Long courseScheduleRuleId;

    /**
     * 上课日期（yyyy-MM-dd）
     */
    @Schema(description = "上课日期（yyyy-MM-dd）")
    private LocalDate attendClassDate;

    /**
     * 上课开始时间（HH:mm）
     */
    @Schema(description = "上课开始时间（HH:mm）")
    private LocalTime attendClassStartTime;

    /**
     * 上课结束时间（HH:mm）
     */
    @Schema(description = "上课结束时间（HH:mm）" )
    private LocalTime attendClassEndTime;

    /**
     * 是否已同步声网创建课堂: 0-否; 1-是;
     */
    @Schema(description = "是否已同步声网创建课堂: 0-否; 1-是; 字典类型：is_sync_agora" ,type = "is_sync_agora")
    private Integer isSyncAgora;

    /**
     * 上课类型: 0-直播课; 1-点播课;
     */
    @Schema(description = "上课类型: 0-直播课; 1-点播课; 字典类型：attend_class_type" ,type = "attend_class_type")
    private Integer attendClassType;

    /**
     * 监课链接url路径
     */
    @Schema(description = "监课链接url路径")
    private String supervisionClassUrl;

    /**
     * 监课开始时间(yyyy-MM-dd HH:mm:ss）
     */
    @Schema(description = "监课开始时间(yyyy-MM-dd HH:mm:ss） 字典类型：supervision_class_start_time" ,type = "supervision_class_start_time")
    private LocalDateTime supervisionClassStartTime;

    /**
     * 监课结束时间(yyyy-MM-dd HH:mm:ss）
     */
    @Schema(description = "监课结束时间(yyyy-MM-dd HH:mm:ss） 字典类型：supervision_class_end_time" ,type = "supervision_class_end_time")
    private LocalDateTime supervisionClassEndTime;

    /**
     * 主讲老师ID(ss_lecturer主键ID)
     */
    @Schema(description = "主讲老师ID(ss_lecturer主键ID)")
    private Long lecturerId;

    /**
     * 主讲设备ID
     */
    @Schema(description = "主讲设备ID")
    private Long deviceId;

    /**
     * 主讲教室ID
     */
    @Schema(description = "主讲教室ID")
    private Long classRoomId;

    /**
     * 书籍ID
     */
    @Schema(description = "书籍ID")
    private String booksId;

    /**
     * 书籍名称
     */
    @Schema(description = "书籍名称")
    private String booksName;

    /**
     * 课程库ID(录播课资源ID)
     */
    @Schema(description = "课程库ID(录播课资源ID)")
    private Long recordingId;

    /**
     * 主讲端上课码(上课端标识1 + 5位随机数  例:115329)
     */
    @Schema(description = "主讲端上课码(上课端标识1 + 5位随机数  例:115329) 字典类型：lecturer_room_code" ,type = "lecturer_room_code")
    private String lecturerRoomCode;

    /**
     * 教室端上课码(教室端标识2 + 5位随机数  例:235329)
     */
    @Schema(description = "教室端上课码(教室端标识2 + 5位随机数  例:235329) 字典类型：class_room_code" ,type = "class_room_code")
    private String classRoomCode;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime ctime;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    private String creator;

    /**
     * 编辑时间
     */
    @Schema(description = "编辑时间")
    private LocalDateTime mtime;

    /**
     * 编辑者
     */
    @Schema(description = "编辑者")
    private String modifer;

    /**
     * 删除标记
     */
    @Schema(description = "删除标记")
    private Integer delFlag;


    /**
     * 课次开始时间
     */
    @Schema(description = "课次开始时间")
    private LocalDateTime attendTimeStartTime;

    /**
     * 课次结束时间
     */
    @Schema(description = "课次结束时间")
    private LocalDateTime attendTimeEndTime;

    /**
     * 老师名称
     */
    @Schema(description = "老师名称")
    private String LecturerName;

    /**
     * 班级名称
     */
    @Schema(description = "班级名称")
    private String className;


    /**
     *  年级
     */
    @Schema(description = "年级",type = "grade")
    private Integer grade;


    /**
     *  星期
     */
    @Schema(description = "星期",type = "week_type")
    private Integer week;


    /**
     *   课次状态
     */
    @Schema(description = "课次状态",type = "class_time_state")
    private Integer attendClassTimeState;


    /**
     * 已开始时长(进行中课程)
     */
    @Schema(description = "已开始时长")
    private Long hasStartTime;



    /**
     * 距离开始时长(即将开始课程)
     */
    @Schema(description = "距离开始时长")
    private Long distanceStartTime;


    /**
     * 学生数
     */
    @Schema(description = "学生数")
    private Long studentCount;


    /**
     * 进入房间token
     */
    @Schema(description = "进入房间token")
    private String rtmToken;

    /**
     * 声网唯一标识
     */
    @Schema(description = "声网唯一标识")
    private String appId;


    /**
     * 点播课资源路径
     */
    @Schema(description = "点播课资源路径")
    private String hillSeedingUrl;

    /**
     * 设备号
     */
    @Schema(description = "设备号")
    private String deviceNo;

}
