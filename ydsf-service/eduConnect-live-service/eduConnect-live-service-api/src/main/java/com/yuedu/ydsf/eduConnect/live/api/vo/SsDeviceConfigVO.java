package com.yuedu.ydsf.eduConnect.live.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 设备配置参数表
 *
 * <AUTHOR>
 * @date 2024/09/28
 */
@Data
@Schema(description = "设备配置参数表展示对象")
public class SsDeviceConfigVO implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  /** 主键ID */
  @Schema(description = "主键ID")
  private Long id;

  /** 教师端大流宽度 */
  @Schema(description = "教师端大流宽度")
  private Integer tBw;

  /** 教师端大流高度 */
  @Schema(description = "教师端大流高度")
  private Integer tBh;

  /** 教师端大码率 */
  @Schema(description = "教师端大码率")
  private Integer tBb;

  /** 教师端大帧率 */
  @Schema(description = "教师端大帧率")
  private Integer tBf;

  /** 教师端小流宽度 */
  @Schema(description = "教师端小流宽度")
  private Integer tSw;

  /** 教师端小流高度 */
  @Schema(description = "教师端小流高度")
  private Integer tSh;

  /** 教师端小码率 */
  @Schema(description = "教师端小码率")
  private Integer tSb;

  /** 教师端小帧率 */
  @Schema(description = "教师端小帧率")
  private Integer tSf;

  /** 学生端大流宽度 */
  @Schema(description = "学生端大流宽度")
  private Integer sBw;

  /** 学生端大流高度 */
  @Schema(description = "学生端大流高度")
  private Integer sBh;

  /** 学生端大码率 */
  @Schema(description = "学生端大码率")
  private Integer sBb;

  /** 学生端大帧率 */
  @Schema(description = "学生端大帧率")
  private Integer sBf;

  /** 学生端小流宽度 */
  @Schema(description = "学生端小流宽度")
  private Integer sSw;

  /** 学生端小流高度 */
  @Schema(description = "学生端小流高度")
  private Integer sSh;

  /** 学生端小码率 */
  @Schema(description = "学生端小码率")
  private Integer sSb;

  /** 学生端小帧率 */
  @Schema(description = "学生端小帧率")
  private Integer sSf;

  /** 教师是否订阅大流:0-是;1-否 */
  @Schema(description = "教师是否订阅大流:0-是;1-否")
  private Byte tHd;

  /** 学生端是否订阅大流:0-是;1-否 */
  @Schema(description = "学生端是否订阅大流:0-是;1-否")
  private Byte sHd;

  /** 九空格展示学生数量 */
  @Schema(description = "九空格展示学生数量")
  private Integer sShowNumber;

  /** 备注 */
  @Schema(description = "备注")
  private String remark;

  /** 创建时间 */
  @Schema(description = "创建时间")
  private LocalDateTime ctime;

  /** 创建者 */
  @Schema(description = "创建者")
  private String creator;

  /** 编辑时间 */
  @Schema(description = "编辑时间")
  private LocalDateTime mtime;

  /** 编辑者 */
  @Schema(description = "编辑者")
  private String modifer;

  /** 日志开关是否开启:0-关闭;1-开启 */
  @Schema(description = "日志开关是否开启:0-关闭;1-开启")
  private Byte logEnable;

  /** 是否删除:0-未删除;1-已删除 */
  @Schema(description = "是否删除:0-未删除;1-已删除")
  private Integer delFlag;

  /** 声网提供的AppId */
  @Schema(description = "声网提供的AppId")
  private String appId;

  /** 互动H5url */
  @Schema(description = "互动H5url")
  private String interactH5url;

  /** 终端设置Vo */
  @Schema(description = "终端设置Vo")
  private SsDeviceSettingVO deviceConfig;

  /** 终端音频配置 */
  @Schema(description = "终端音频配置")
  private SsDeviceAudioSettingVO audioConfig;
}
