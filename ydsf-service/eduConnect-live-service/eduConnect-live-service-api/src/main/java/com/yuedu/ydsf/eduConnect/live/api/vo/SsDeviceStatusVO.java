package com.yuedu.ydsf.eduConnect.live.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 双师设备状态Vo对象
 *
 * <AUTHOR> href="">liuyi/a>
 * @date 2024/9/28 13:42
 * @project @Title: SsDeviceStatusVo.java
 */
@Data
@Schema(description = "双师设备状态Vo对象")
public class SsDeviceStatusVO {

  /** 设备状态0-正常；1-异常 */
  private Integer statusCode = 0;

  /** 异常提示语 */
  private String errorMessage;

  /** 图片二维码地址（收款二维码） */
  private String imageUrl;

  /** 设备信息 */
  private SsDeviceVO deviceVo;

  /** 配置相关 */
  private SsDeviceConfigVO ssConfigVo;
}
