package com.yuedu.ydsf.eduConnect.live.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * @ClassName RecordingVerificationVO
 * @Description 录课视频验课VO
 * <AUTHOR>
 * @Date 2024/12/11 16:20
 * @Version v0.0.1
 */

@Data
public class RecordingVerificationVO {
    /**
     * 点播课程Id
     */
    @Schema(description = "点播课程Id")
    private Long id;

    /**
     * 录制资源
     */
    @Schema(description="录制资源")
    private String recordingResources;

    /**
     * 录播任务Id
     */
    @Schema(description = "录播任务Id")
    private Long recordVideoTaskId;

    /**
     * 教学计划排期Id
     */
    @Schema(description = "教学计划Id")
    private Long teachingPlanId;

    /**
     * 直播间计划Id
     */
    @Schema(description="直播间计划Id")
    private Long liveRoomPlanId;

    /**
     * 计划名称
     */
    @Schema(description="计划名称")
    private String planName;

    /**
     * 课节名称
     */
    @Schema(description = "课节名称")
    private String lessonName;

    /**
     * 主讲老师姓名
     */
    @Schema(description = "主讲老师姓名")
    private String name;

    /**
     * 年级(阶段)
     */
    @Schema(description="年级(阶段)")
    private Integer stage;

    /**
     * 最早开始日期
     */
    @Schema(description="上课日期时段")
    private LocalDateTime earliestStartDate;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
}
