package com.yuedu.ydsf.eduConnect.live.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 学生答题器更新结果VO
 *
 * <AUTHOR>
 * @date 2025/07/10
 */
@Data
@Schema(description = "学生答题器更新结果VO")
public class StudentClickerUpdateResultVO implements Serializable {

    /**
     * 学生课次ID
     */
    @Schema(description = "学生课次ID")
    private Long classStudentId;

    /**
     * 接收器SN码
     */
    @Schema(description = "接收器SN码")
    private String receiverSnNumber;

    /**
     * 答题器SN码
     */
    @Schema(description = "答题器SN码")
    private String snNumber;

    /**
     * 是否成功
     */
    @Schema(description = "是否成功")
    private Boolean success;

    /**
     * 结果消息
     */
    @Schema(description = "结果消息")
    private String message;

    /**
     * 错误详情（失败时提供）
     */
    @Schema(description = "错误详情")
    private String errorDetail;

    /**
     * 创建成功结果
     */
    public static StudentClickerUpdateResultVO success(Long classStudentId, String receiverSnNumber, String snNumber) {
        StudentClickerUpdateResultVO result = new StudentClickerUpdateResultVO();
        result.setClassStudentId(classStudentId);
        result.setReceiverSnNumber(receiverSnNumber);
        result.setSnNumber(snNumber);
        result.setSuccess(true);
        result.setMessage("更新成功");
        return result;
    }

    /**
     * 创建失败结果
     */
    public static StudentClickerUpdateResultVO failure(Long classStudentId, String receiverSnNumber, String snNumber, String errorMessage, String errorDetail) {
        StudentClickerUpdateResultVO result = new StudentClickerUpdateResultVO();
        result.setClassStudentId(classStudentId);
        result.setReceiverSnNumber(receiverSnNumber);
        result.setSnNumber(snNumber);
        result.setSuccess(false);
        result.setMessage(errorMessage);
        result.setErrorDetail(errorDetail);
        return result;
    }
}
