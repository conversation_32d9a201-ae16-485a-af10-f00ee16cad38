package com.yuedu.ydsf.eduConnect.live.api.vo;

import lombok.Data;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;

/**
* 上课拍照记录
*
* <AUTHOR>
* @date  2025/02/13
*/
@Data
@Schema(description = "上课拍照记录展示对象")
public class TimetablePictureVO {


    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 上课课次ID
     */
    @Schema(description = "上课课次ID")
    private Long timeableId;

    /**
     * 拍照设备ID
     */
    @Schema(description = "拍照设备ID")
    private Long deviceId;

    /**
     * 拍照照片url
     */
    @Schema(description = "拍照照片url")
    private String photoUrl;

    /**
     * 识别总数量
     */
    @Schema(description = "识别总数量")
    private Integer recognitionTotalNum;

    /**
     * 识别幼儿数量
     */
    @Schema(description = "识别幼儿数量")
    private Integer recognitionChildrenNum;

    /**
     * 识别青少年数量
     */
    @Schema(description = "识别青少年数量")
    private Integer recognitionTeenagersNum;

    /**
     * 识别青年数量
     */
    @Schema(description = "识别青年数量")
    private Integer recognitionYouthNum;

    /**
     * 识别中年数量
     */
    @Schema(description = "识别中年数量")
    private Integer recognitionMiddleNum;

    /**
     * 识别老年数量
     */
    @Schema(description = "识别老年数量")
    private Integer recognitionElderlyNum;

    /**
     * 识别状态: 0-未处理; 1-处理成功; 2-处理失败
     */
    @Schema(description = "识别状态: 0-未处理; 1-处理成功; 2-处理失败 字典类型：recognition_status" ,type = "recognition_status")
    private Integer recognitionStatus;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 是否删除: 0-否; 1-是;
     */
    @Schema(description = "是否删除: 0-否; 1-是; 字典类型：del_flag" ,type = "del_flag")
    private Integer delFlag;

    /**
     * 计数第几张
     */
    @Schema(description = "计数第几张")
    private Integer sort;

    /**
     * 距离上课时长,单位：秒
     */
    @Schema(description = "距离上课时长")
    private Integer duration;


    /**
     * 上课课号
     */
    @Schema(description = "上课课号")
    private Long lessionNo;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 距离当前时间间的延迟时间,单位：秒
     */
    private Long delayTime;

    /**
     * 系统时间
     */
    private LocalDateTime systemTime;

}
