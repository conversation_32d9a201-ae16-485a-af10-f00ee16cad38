package com.yuedu.ydsf.eduConnect.live.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName CourseVodVideoVO
 * @Description 点播课程视频VO
 * <AUTHOR>
 * @Date 2024/12/11 15:09
 * @Version v0.0.1
 */

@Data
public class CourseVodVideoVO {
    /**
     * 主键
     */
    @Schema(description="主键")
    private Long id;

    /**
     * 点播课ID
     */
    @Schema(description="点播课ID")
    private Long courseVodId;

    /**
     * 阿里云视频点播ID
     */
    @Schema(description="阿里云视频点播ID")
    private String aliyunVodId;

    /**
     * 阿里云视频播放地址
     */
    @Schema(description="阿里云视频播放地址")
    private String aliyunPlayUrl;

    /**
     * mp4视频地址
     */
    @Schema(description="mp4视频地址")
    private String mp4Url;

    /**
     * 录制任务ID
     */
    @Schema(description="录制任务ID")
    private Long recordVideoTaskId;
}
