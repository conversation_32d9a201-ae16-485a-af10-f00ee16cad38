package com.yuedu.ydsf.eduConnect.live.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import lombok.Data;

/**
 * 互动结果表
 *
 * <AUTHOR>
 * @date 2024/11/05
 */
@Data
@Schema(description = "互动结果表展示对象")
public class SsInteractionConsequenceResultVO {

  /** 红包总量 */
  @Schema(description = "红包总量")
  private Integer integralTotal;

  /** 已领取数量 */
  @Schema(description = "已领取数量")
  private Integer alreadyIntegralReceived = 0;

  /** 选项数量 */
  @Schema(description = "选项数量")
  private OptionNum optionNum;

  /** 总参与人数 */
  @Schema(description = "总参与人数")
  private Integer totalParticipants = 0;

  /** 全国互动详情 */
  private List<SsInteractionConsequenceVO> nationwideResult;

  @Data
  public static class OptionNum {

    /** A选项数量 */
    private Integer Anum = 0;

    /** B选项数量 */
    private Integer Bnum = 0;

    /** C选项数量 */
    private Integer Cnum = 0;

    /** D选项数量 */
    private Integer Dnum = 0;

    /** E选项数量 */
    private Integer Enum = 0;

    /** F选项数量 */
    private Integer Fnum = 0;
  }
}
