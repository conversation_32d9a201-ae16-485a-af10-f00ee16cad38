package com.yuedu.ydsf.eduConnect.live.api.vo;

import lombok.Data;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;

/**
* 点播库
*
* <AUTHOR>
* @date  2024/11/01
*/
@Data
@Schema(description = "点播库展示对象")
public class SsRecordingVO {


    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 录制类型:0-点播课;1-培训会议
     */
    @Schema(description = "录制类型:0-点播课;1-培训会议 字典类型：recording_type" ,type = "recording_type")
    private Integer recordingType;

    /**
     * 录制设备ID
     */
    @Schema(description = "录制设备ID")
    private Long deviceId;

    /**
     * 年级:1-lv1;2-lv2;3-lv3;4-lv4;5-lv5;6-lv6;7-lv7
     */
    @Schema(description = "年级:1-lv1;2-lv2;3-lv3;4-lv4;5-lv5;6-lv6;7-lv7 字典类型：grade" ,type = "grade")
    private Integer grade;

    /**
     * 书籍ID
     */
    @Schema(description = "书籍ID")
    private String booksId;

    /**
     * 书籍名称
     */
    @Schema(description = "书籍名称")
    private String booksName;

    /**
     * 主讲老师ID(ss_lecturer主键ID)
     */
    @Schema(description = "主讲老师ID(ss_lecturer主键ID)")
    private Long lecturerId;

    /**
     * 主讲姓名
     */
    @Schema(description = "主讲姓名")
    private String lecturerName;

    /**
     * 原考勤班级上课开始时间
     */
    @Schema(description = "原考勤班级上课开始时间")
    private LocalDateTime originalCourseStartDate;

    /**
     * 原考勤班级上课结束时间
     */
    @Schema(description = "原考勤班级上课结束时间")
    private LocalDateTime originalCourseEndDate;

    /**
     * 声网录制ID
     */
    @Schema(description = "声网录制ID")
    private String agoraRecordId;

    /**
     * 声网房间UUID
     */
    @Schema(description = "声网房间UUID")
    private String roomUuid;

    /**
     * 上下架状态:0-未上架;1-已上架
     */
    @Schema(description = "上下架状态:0-未上架;1-已上架 字典类型：shelf_status" ,type = "shelf_status")
    private Integer shelfStatus;

    /**
     * 录制状态:0-待录制;1-录制中;2-正常录制完成;3-录制作废(重新录制);4-视频处理中;5-视频处理失败;6-停止录制;7-转码中;8-转码失败
     */
    @Schema(description = "录制状态:0-待录制;1-录制中;2-正常录制完成;3-录制作废(重新录制);4-视频处理中;5-视频处理失败;6-停止录制;7-转码中;8-转码失败 字典类型：recording_status" ,type = "recording_status")
    private Integer recordingStatus;

    /**
     * 资源存储类型:0-OSS;1-VOD
     */
    @Schema(description = "资源存储类型:0-OSS;1-VOD 字典类型：storage_type" ,type = "storage_type")
    private Integer storageType;

    /**
     * 视频点播Vod中videoId
     */
    @Schema(description = "视频点播Vod中videoId")
    private String vodVideoId;

    /**
     * 录制资源
     */
    @Schema(description = "录制资源")
    private String recordingResources;

    /**
     * 声网云端录制id
     */
    @Schema(description = "声网云端录制id")
    private String agoraCloudRecordId;

    /**
     * 云端录制资源地址
     */
    @Schema(description = "云端录制资源地址")
    private String cloudRecordingResources;

    /**
     * 声网单流云录制iD
     */
    @Schema(description = "声网单流云录制iD")
    private String agoraCloudRecordIndividualResourceId;

    /**
     * 声网云端录制单流id
     */
    @Schema(description = "声网云端录制单流id")
    private String agoraCloudRecordIndividualId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime ctime;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    private String creator;

    /**
     * 编辑时间
     */
    @Schema(description = "编辑时间")
    private LocalDateTime mtime;

    /**
     * 编辑人
     */
    @Schema(description = "编辑人")
    private String modifer;

    /**
     * 逻辑删除: 0-正常;1-删除
     */
    @Schema(description = "逻辑删除: 0-正常;1-删除 字典类型：del_flag" ,type = "del_flag")
    private Integer delFlag;

    /**
     * 下载地址
     */
    @Schema(description = "下载地址")
    private String downloadUrl;

    /**
     * 下载状态: 0-不可用; 1-可用; 2-错误
     */
    @Schema(description = "下载状态: 0-不可用; 1-可用; 2-错误 字典类型：download_status" ,type = "download_status")
    private Integer downloadStatus;

    /**
     * 下载Vod的Id
     */
    @Schema(description = "下载Vod的Id")
    private String downloadVodId;

    /**
     * 提交状态: 0-未提交; 1-待提交; 2-已提交; 3-回收站
     */
    @Schema(description = "提交状态: 0-未提交; 1-待提交; 2-已提交; 3-回收站 字典类型：audit_status" ,type = "audit_status")
    private Integer auditStatus;

    /**
     * rtmToken
     */
    @Schema(description = "rtmToken")
    private String rtmToken;

    /**
     * appId
     */
    @Schema(description = "appId")
    private String appId;

    /**
     * 录制时间
     */
    @Schema(description = "录制时间")
    private LocalDateTime recordingTime;

}
