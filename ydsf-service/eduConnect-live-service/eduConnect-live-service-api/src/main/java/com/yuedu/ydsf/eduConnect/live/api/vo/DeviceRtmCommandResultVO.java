package com.yuedu.ydsf.eduConnect.live.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 设备RTM命令结果VO
 *
 * @date 2024/12/19
 */
@Data
@Schema(description = "设备RTM命令结果VO")
public class DeviceRtmCommandResultVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 命令ID
     */
    @Schema(description = "命令ID")
    private String commandId;

    /**
     * 设备编号
     */
    @Schema(description = "设备编号")
    private String deviceNo;

    /**
     * 设备ID
     */
    @Schema(description = "设备ID")
    private String deviceId;

    /**
     * 命令类型
     */
    @Schema(description = "命令类型")
    private Integer commandType;

    /**
     * 命令描述
     */
    @Schema(description = "命令描述")
    private String commandDesc;

    /**
     * 发送状态
     */
    @Schema(description = "发送状态：0-发送成功，1-发送失败")
    private Integer sendStatus;

    /**
     * 发送结果消息
     */
    @Schema(description = "发送结果消息")
    private String sendMessage;

    /**
     * RTM消息ID
     */
    @Schema(description = "RTM消息ID")
    private String rtmMessageId;

    /**
     * 发送时间
     */
    @Schema(description = "发送时间")
    private LocalDateTime sendTime;
}
