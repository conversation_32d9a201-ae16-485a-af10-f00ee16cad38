package com.yuedu.ydsf.eduConnect.live.api.vo;

import com.yuedu.ydsf.common.core.util.V_A_E;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * SsRTMResultVO
 *
 * <AUTHOR>
 * @date 2024/09/26
 */
@Data
@Schema(description = "RTM结果返回")
public class SsRTMResultVO implements Serializable {

  @Schema(description = "rtmToken")
  private String rtmToken;

  @Schema(description = "rtmAppId")
  private String rtmAppId;
}
