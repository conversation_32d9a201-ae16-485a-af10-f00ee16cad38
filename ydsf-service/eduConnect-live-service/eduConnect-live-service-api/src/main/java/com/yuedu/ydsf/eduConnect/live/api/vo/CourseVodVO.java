package com.yuedu.ydsf.eduConnect.live.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * @ClassName CourseVodVO
 * @Description 点播课程VO
 * <AUTHOR>
 * @Date 2024/12/6 09:05
 * @Version v0.0.1
 */

@Data
public class CourseVodVO {

    /**
     * 点播课程Id
     */
    @Schema(description = "点播课程Id")
    private Long id;

    /**
     * 点播课程视频Id
     */
    @Schema(description = "点播课程视频Id")
    private Long courseVodVideoId;

    /**
     * 录播任务Id
     */
    @Schema(description = "录播任务Id")
    private Long recordVideoTaskId;

    /**
     * 书籍名称
     */
    @Schema(description = "书籍名称")
    private String bookName;

    /**
     * 主讲老师姓名
     */
    @Schema(description = "主讲老师姓名")
    private String name;

    /**
     * 年级(阶段)
     */
    @Schema(description="年级(阶段)")
    private Integer stage;

    /**
     * 最早开始日期
     */
    @Schema(description="最早开始日期")
    private LocalDateTime earliestStartDate;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 第几节课
     */
    @Schema(description="第几节课")
    private Integer lessonOrder;

    /**
     * 课程id
     */
    private Long courseId;

    /**
     * 主讲id
     */
    @Schema(description = "主讲id")
    private Long lectureId;

    /**
     * 阿里云视频播放地址
     */
    @Schema(description="阿里云视频播放地址")
    private String aliyunPlayUrl;

    /**
     * mp4视频地址
     */
    @Schema(description="mp4视频地址")
    private String mp4Url;

    /**
     * 声网云端录制ID
     */
    @Schema(description="声网云端录制ID")
    private String agoraCloudRecordId;

    /**
     * 课程版本号
     */
    @Schema(description="课程版本号")
    private Integer courseVersion;

    /**
     * 课件版本
     */
    @Schema(description="课件版本")
    private Integer coursewareVersion;

    /**
     * 录课时间
     */
    @Schema(description = "录课时间")
    private LocalDateTime recordingTime;

    /**
     * 课件ID
     */
    @Schema(description="课件ID")
    private Long coursewareId;

}
