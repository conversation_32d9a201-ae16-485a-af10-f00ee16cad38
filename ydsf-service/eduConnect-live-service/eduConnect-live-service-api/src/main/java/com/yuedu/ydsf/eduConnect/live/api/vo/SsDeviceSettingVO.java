package com.yuedu.ydsf.eduConnect.live.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serial;
import java.io.Serializable;

/**
 * 设备设置返回实体
 *
 * <AUTHOR> href="https://www.6yi.plus">刘艺</a>
 * @date 2024/5/24 15:39
 * @project @Title: SsDeviceSettingVo.java
 */
@Data
@Schema(description = "设备设置返回实体")
public class SsDeviceSettingVO implements Serializable{

  @Serial private static final long serialVersionUID = 1L;

  @Schema(description = "大流宽度")
  private Integer bw;

  @Schema(description = "大流高度")
  private Integer bh;

  @Schema(description = "大码率")
  private Integer bb;

  @Schema(description = "大帧率")
  private Integer bf;

  @Schema(description = "小流宽度")
  private Integer sw;

  @Schema(description = "小流高度")
  private Integer sh;

  @Schema(description = "小码率")
  private Integer sb;

  @Schema(description = "小帧率")
  private Integer sf;

  @Schema(description = "是否订阅大流")
  private Byte hd;

  @Schema(description = "日志开关是否开启: 0-关闭; 1-开启")
  private Byte logEnable;

  @Schema(description = "九宫格展示学生数量")
  private Integer sShowNumber;
}
