package com.yuedu.ydsf.eduConnect.live.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 设备设置返回实体
 *
 * <AUTHOR> href="">刘艺</a>
 * @date 2024/9/28 14:29
 * @project @Title: SsDeviceAudioSettingVo.java
 */
@Data
@Schema(description = "设备音频相关配置展示对象")
public class SsDeviceAudioSettingVO implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  /** 参数数组 */
  @Schema(description = "参数数组")
  private List<String> parametersArr;

  /** 音量增益 */
  @Schema(description = "音量增益")
  private Integer adjustRecordingSignalVolume;
}
