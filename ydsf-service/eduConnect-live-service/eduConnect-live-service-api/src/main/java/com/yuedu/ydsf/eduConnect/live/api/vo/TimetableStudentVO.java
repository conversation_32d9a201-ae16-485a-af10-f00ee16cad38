package com.yuedu.ydsf.eduConnect.live.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 教室端-读书会学生视图类
 * <AUTHOR>
 * @date 2025/1/8 20:13
 */
@Data
@Schema(description = "教室端-读书会学生视图类")
public class TimetableStudentVO {

    /**
     * 学生ID
     */
    @Schema(description="学生ID")
    private String studentId;

    /**
     * 学生手机号
     */
    @Schema(description="学生手机号")
    private String studentMobile;

    /**
     * 学生名称
     */
    @Schema(description="学生名称")
    private String studentName;

}

