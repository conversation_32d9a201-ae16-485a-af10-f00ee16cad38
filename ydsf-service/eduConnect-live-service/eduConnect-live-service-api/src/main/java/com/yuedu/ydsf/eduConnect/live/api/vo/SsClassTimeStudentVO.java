package com.yuedu.ydsf.eduConnect.live.api.vo;

import lombok.Data;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;

/**
* 校区上课学生表
*
* <AUTHOR>
* @date  2024/11/04
*/
@Data
@Schema(description = "校区上课学生表展示对象")
public class SsClassTimeStudentVO {


    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 班级ID
     */
    @Schema(description = "班级ID")
    private Long classId;

    /**
     * 课次ID
     */
    @Schema(description = "课次ID")
    private Long classTimeId;

    /**
     * 课次授权教室表ID
     */
    @Schema(description = "课次授权教室表ID")
    private Long classTimeAuthRoomId;

    /**
     * 校区ID
     */
    @Schema(description = "校区ID")
    private Long campusId;

    /**
     * 教室ID
     */
    @Schema(description = "教室ID")
    private Long classRoomId;

    /**
     * 教室设备ID
     */
    @Schema(description = "教室设备ID")
    private Long deviceId;

    /**
     * 校管家学生ID
     */
    @Schema(description = "校管家学生ID")
    private String studentId;

    /**
     * 校管家学生手机号
     */
    @Schema(description = "校管家学生手机号")
    private String studentMobile;

    /**
     * 校管家学生名称
     */
    @Schema(description = "校管家学生名称")
    private String studentName;

    /**
     * 互动题绑定状态: 0-未绑定; 1-已绑定;(废弃)
     */
    @Schema(description = "互动题绑定状态: 0-未绑定; 1-已绑定;(废弃) 字典类型：interactor_bind_status" ,type = "interactor_bind_status")
    private Integer interactorBindStatus;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime ctime;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    private String creator;

    /**
     * 编辑时间
     */
    @Schema(description = "编辑时间")
    private LocalDateTime mtime;

    /**
     * 编辑者
     */
    @Schema(description = "编辑者")
    private String modifer;

    /**
     * 删除标识
     */
    @Schema(description = "删除标识")
    private Integer delFlag;

    /**
     * 学生数
     */
    @Schema(description = "学生数")
    private Long studentCount;

}
