package com.yuedu.ydsf.eduConnect.live.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

/**
 * 教室端-读书会 视图类
 * <AUTHOR>
 * @date 2024/12/17 18:13
 */
@Data
@Schema(description = "教室端-读书会视图类")
public class TimeTypeTimetableVO {

    /**
     * 上课时段类型:1-上午;2-下午;3-晚上
     */
    @Schema(description="时段类型:1-上午;2-下午;3-晚上")
    private Integer timeType;

    /**
     * 教室端课表
     */
    @Schema(description="教室端课表")
    private List<TimetableVO> timetableVOList;

}

