package com.yuedu.ydsf.eduConnect.live.api.vo;

import com.yuedu.ydsf.eduConnect.api.constant.PcEnterStatusTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 进入房间错误返回类
 *
 * @date 2025/5/7 11:41
 * @project @Title: EnterLiveErrorVO.java
 */
@Data
public class EnterLiveErrorVO {

  public EnterLiveErrorVO() {}

  public EnterLiveErrorVO(PcEnterStatusTypeEnum errorEnum) {
    this.code = errorEnum.getCode();
    this.msg = errorEnum.getDescription();
  }

  /** 错误码 */
  @Schema(description = "code")
  private String code = "0";

  /** 错误信息 */
  @Schema(description = "错误信息")
  private String msg;

}
