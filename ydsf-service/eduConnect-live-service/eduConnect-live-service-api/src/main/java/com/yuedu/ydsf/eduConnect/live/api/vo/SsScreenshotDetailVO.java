package com.yuedu.ydsf.eduConnect.live.api.vo;

import lombok.Data;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;

/**
* 双师截图明细表
*
* <AUTHOR>
* @date  2025/02/18
*/
@Data
@Schema(description = "双师截图明细表展示对象")
public class SsScreenshotDetailVO {


    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 课次ID
     */
    @Schema(description = "课次ID")
    private Long classTimeId;

    /**
     * 教室端设备ID
     */
    @Schema(description = "教室端设备ID")
    private Long deviceId;

    /**
     * 资源名称
     */
    @Schema(description = "资源名称")
    private String resourcesName;

    /**
     * 资源路径
     */
    @Schema(description = "资源路径")
    private String resourcesPath;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime ctime;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    private String creator;

    /**
     * 编辑时间
     */
    @Schema(description = "编辑时间")
    private LocalDateTime mtime;

    /**
     * 编辑人
     */
    @Schema(description = "编辑人")
    private String modifer;

    /**
     * 是否删除: 0-未删除;1-已删除
     */
    @Schema(description = "是否删除: 0-未删除;1-已删除 字典类型：del_flag" ,type = "del_flag")
    private Integer delFlag;

    /**
     * 截图时间
     */
    @Schema(description = "截图时间")
    private LocalDateTime screenshotTime;

    /**
     * 识别总数量
     */
    @Schema(description = "识别总数量")
    private Integer recognitionTotalNum;

    /**
     * 识别幼儿数量
     */
    @Schema(description = "识别幼儿数量")
    private Integer recognitionChildrenNum;

    /**
     * 识别青少年数量
     */
    @Schema(description = "识别青少年数量")
    private Integer recognitionTeenagersNum;

    /**
     * 识别青年数量
     */
    @Schema(description = "识别青年数量")
    private Integer recognitionYouthNum;

    /**
     * 识别中年数量
     */
    @Schema(description = "识别中年数量")
    private Integer recognitionMiddleNum;

    /**
     * 识别老年数量
     */
    @Schema(description = "识别老年数量")
    private Integer recognitionElderlyNum;

    /**
     * 识别状态: 0-未处理; 1-处理成功; 2-处理失败
     */
    @Schema(description = "识别状态: 0-未处理; 1-处理成功; 2-处理失败 字典类型：recognition_status" ,type = "recognition_status")
    private Integer recognitionStatus;

}
