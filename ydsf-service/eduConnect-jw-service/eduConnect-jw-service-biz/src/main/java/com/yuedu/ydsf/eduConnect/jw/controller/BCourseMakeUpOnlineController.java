package com.yuedu.ydsf.eduConnect.jw.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.core.util.V_A;
import com.yuedu.ydsf.common.core.util.V_E;
import com.yuedu.ydsf.common.excel.annotation.ResponseExcel;
import com.yuedu.ydsf.common.idempotent.annotation.Idempotent;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.security.annotation.StorePermission;
import com.yuedu.ydsf.eduConnect.jw.api.valid.BMakeUpOnlineValidGroup;
import com.yuedu.ydsf.eduConnect.jw.api.vo.MakeUpOnlineErrorVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import com.yuedu.ydsf.eduConnect.jw.service.BCourseMakeUpOnlineService;
import com.yuedu.ydsf.eduConnect.jw.api.query.BCourseMakeUpOnlineQuery;
import com.yuedu.ydsf.eduConnect.jw.api.dto.BCourseMakeUpOnlineDTO;
import com.yuedu.ydsf.eduConnect.jw.api.vo.BCourseMakeUpOnlineVO;

import java.io.Serializable;
import java.util.List;

/**
 * 门店线上补课表控制层
 *
 * <AUTHOR>
 * @date 2025/04/23
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/bCourseMakeUpOnline")
@Tag(description = "b_course_make_up_online", name = "门店线上补课表")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class BCourseMakeUpOnlineController {

  private final BCourseMakeUpOnlineService courseMakeUpOnlineService;

  /**
   * 门店线上补课表分页查询
   *
   * @param page 分页对象
   * @param bCourseMakeUpOnlineQuery 门店线上补课表
   * @return R
   */
  @GetMapping("/page")
  @HasPermission("bCourseMakeUpOnline_view")
  @Operation(summary = "分页查询", description = "门店线上补课表分页查询")
  public R page(
      @ParameterObject Page page,
      @ParameterObject BCourseMakeUpOnlineQuery bCourseMakeUpOnlineQuery) {
    return R.ok(courseMakeUpOnlineService.page(page, bCourseMakeUpOnlineQuery));
  }

  /**
   * 通过id查询门店线上补课表
   *
   * @param id id
   * @return R
   */
  @Operation(summary = "通过id查询", description = "通过id查询门店线上补课表")
  @GetMapping("/{id}")
  @HasPermission("bCourseMakeUpOnline_view")
  public R getById(@PathVariable Serializable id) {
    return R.ok(courseMakeUpOnlineService.getById(id));
  }

  /**
   * 新增门店线上补课表
   *
   * @param bCourseMakeUpOnlineDTO 门店线上补课表
   * @return R
   */
  @PostMapping
  @SysLog("新增门店线上补课")
  @Operation(summary = "新增门店线上补课", description = "新增门店线上补课")
  @StorePermission
  @Idempotent(key = "'addMakeUpOnline:' + #bCourseMakeUpOnlineDTO.lessonNo", expireTime = 15, info = "新增线上补课操作进行中，请稍后再试")
  public R add(
      @Validated(BMakeUpOnlineValidGroup.AddMakeUpOnline.class) @RequestBody
          BCourseMakeUpOnlineDTO bCourseMakeUpOnlineDTO) {
    return R.ok(courseMakeUpOnlineService.add(bCourseMakeUpOnlineDTO));
  }

  /**
   * 编辑线上补课
   *
   * <AUTHOR>
   * @date 2025/4/24 9:03
   * @param bCourseMakeUpOnlineDto
   * @return
   *     com.yuedu.ydsf.common.core.util.R<com.yuedu.ydsf.eduConnect.jw.api.vo.BCourseMakeUpOnlineVO>
   */
  @PutMapping("/edit")
  @SysLog("编辑线上补课")
  @Operation(summary = "编辑线上补课", description = "编辑线上补课")
  @StorePermission
  @Idempotent(key = "'editMakeUpOnline:' + #bCourseMakeUpOnlineDto.id", expireTime = 12, info = "编辑线上补课操作进行中，请稍后再试")
  public R<BCourseMakeUpOnlineVO> edit(
      @Validated(BMakeUpOnlineValidGroup.EditMakeUpOnline.class) @RequestBody
          BCourseMakeUpOnlineDTO bCourseMakeUpOnlineDto) {
    courseMakeUpOnlineService.edit(bCourseMakeUpOnlineDto);
    return R.ok();
  }

  /**
   * 校验补课课节是否存在补课视频
   *
   * <AUTHOR>
   * @date 2025/4/24 9:15
   * @param bCourseMakeUpOnlineQuery
   * @return com.yuedu.ydsf.common.core.util.R
   */
  @GetMapping("/checkHasResources")
  @SysLog("校验补课课节是否存在补课视频")
  @Operation(summary = "校验补课课节是否存在补课视频", description = "校验补课课节是否存在补课视频")
  @StorePermission
  public R checkHasResources(
      @Validated(BMakeUpOnlineValidGroup.CheckHasResources.class) @ParameterObject
          BCourseMakeUpOnlineQuery bCourseMakeUpOnlineQuery) {
    return courseMakeUpOnlineService.checkHasResources(bCourseMakeUpOnlineQuery);
  }

  /**
   * 获取补课详情-新增
   *
   * <AUTHOR>
   * @date 2025/4/23 14:14
   * @param bCourseMakeUpOnlineQuery
   * @return com.yuedu.ydsf.common.core.util.R
   */
  @GetMapping("/add/getMakeUpOnlineInfo")
  @SysLog("获取补课详情-新增")
  @Operation(summary = "获取补课详情-新增", description = "获取补课详情-新增")
  @StorePermission
  public R<BCourseMakeUpOnlineVO> getMakeUpOnlineInfoAdd(
      @Validated(BMakeUpOnlineValidGroup.GetMakeUpOnlineInfoAdd.class) @ParameterObject
          BCourseMakeUpOnlineQuery bCourseMakeUpOnlineQuery) {
    BCourseMakeUpOnlineVO courseMakeUpOnlineVO =
        courseMakeUpOnlineService.getMakeUpOnlineInfoAdd(bCourseMakeUpOnlineQuery);
    return R.ok(courseMakeUpOnlineVO);
  }

  /**
   * 获取补课详情-编辑
   *
   * <AUTHOR>
   * @date 2025/4/23 14:14
   * @param bCourseMakeUpOnlineQuery
   * @return com.yuedu.ydsf.common.core.util.R
   */
  @GetMapping("/edit/getMakeUpOnlineInfo")
  @SysLog("获取补课详情-编辑")
  @Operation(summary = "获取补课详情-编辑", description = "获取补课详情-编辑")
  @StorePermission
  public R<BCourseMakeUpOnlineVO> getMakeUpOnlineInfoEdit(
      @Validated(BMakeUpOnlineValidGroup.GetMakeUpOnlineInfoEdit.class) @ParameterObject
          BCourseMakeUpOnlineQuery bCourseMakeUpOnlineQuery) {
    BCourseMakeUpOnlineVO courseMakeUpOnlineVO =
        courseMakeUpOnlineService.getMakeUpOnlineInfoEdit(bCourseMakeUpOnlineQuery);
    return R.ok(courseMakeUpOnlineVO);
  }

  /**
   * 通过id删除门店线上补课表
   *
   * <AUTHOR>
   * @date 2025/4/24 11:11
   * @param bCourseMakeUpOnlineDto
   * @return com.yuedu.ydsf.common.core.util.R
   */
  @PostMapping("/delete")
  @SysLog("通过id删除门店线上补课表")
  @Operation(summary = "删除门店线上补课表", description = "删除门店线上补课表")
  @StorePermission
  @Idempotent(key = "'deleteMakeUpOnline:' + #bCourseMakeUpOnlineDto.id", expireTime = 10, info = "删除线上补课操作进行中，请稍后再试")
  public R delete(
      @Validated(BMakeUpOnlineValidGroup.DeleteMakeUpOnline.class) @RequestBody
          BCourseMakeUpOnlineDTO bCourseMakeUpOnlineDto) {
    courseMakeUpOnlineService.deleteMakeUpOnlie(bCourseMakeUpOnlineDto);
    return R.ok();
  }
}
