<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.yuedu</groupId>
		<artifactId>ydsf-service</artifactId>
		<version>5.6.16-SNAPSHOT</version>
	</parent>

	<artifactId>eduConnect-system-service</artifactId>
	<version>5.6.16-SNAPSHOT</version>
	<packaging>pom</packaging>
	<properties>
		<maven.compiler.source>17</maven.compiler.source>
		<maven.compiler.target>17</maven.compiler.target>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
		<forest.version>1.5.36</forest.version>
		<ali.vod.version>3.2.0</ali.vod.version>
        <json-path.version>2.9.0</json-path.version>
	</properties>

	<!--项目子模块-->
	<modules>
		<module>eduConnect-system-service-api</module>
		<module>eduConnect-system-service-biz</module>
	</modules>

	<dependencyManagement>
		<dependencies>
			<!--阿里云点播-->
			<dependency>
				<groupId>com.aliyun</groupId>
				<artifactId>vod20170321</artifactId>
				<version>${ali.vod.version}</version>
			</dependency>
			<!--http请求工具类-->
			<dependency>
				<groupId>com.dtflys.forest</groupId>
				<artifactId>forest-spring-boot3-starter</artifactId>
				<version>${forest.version}</version>
			</dependency>
            <dependency>
                <groupId>com.jayway.jsonpath</groupId>
                <artifactId>json-path</artifactId>
                <version>${json-path.version}</version>
            </dependency>
		</dependencies>
	</dependencyManagement>
</project>
