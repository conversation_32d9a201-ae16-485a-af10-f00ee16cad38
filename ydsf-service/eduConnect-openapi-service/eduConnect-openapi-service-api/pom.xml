<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.yuedu</groupId>
		<artifactId>eduConnect-openapi-service</artifactId>
		<version>5.6.16-SNAPSHOT</version>
	</parent>

	<artifactId>eduConnect-openapi-service-api</artifactId>
	<version>5.6.16-SNAPSHOT</version>
	<packaging>jar</packaging>

	<dependencies>
		<!-- 连表查询注解 -->
		<dependency>
			<groupId>com.github.yulichang</groupId>
			<artifactId>mybatis-plus-join-annotation</artifactId>
		</dependency>
		<!--core 工具类-->
		<dependency>
			<groupId>com.yuedu</groupId>
			<artifactId>ydsf-common-core</artifactId>
		</dependency>
		<!--mybatis plus extension,包含了mybatis plus core-->
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-extension</artifactId>
		</dependency>
		<!--feign 工具类-->
		<dependency>
			<groupId>com.yuedu</groupId>
			<artifactId>ydsf-common-feign</artifactId>
		</dependency>
		<!-- excel 导入导出 -->
		<dependency>
			<groupId>com.yuedu</groupId>
			<artifactId>ydsf-common-excel</artifactId>
		</dependency>
	</dependencies>
</project>
